/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "*"
  ],
  theme: {
    extend: {
      colors: {
        // কাস্টম রঙ যোগ করতে পারেন
        
      },
      fontFamily: {
        // কাস্টম ফন্ট যোগ করতে পারেন
        'sans': ['Inter', 'ui-sans-serif', 'system-ui'],
        'serif': ['ui-serif', 'Georgia'],
        'mono': ['ui-monospace', 'SFMono-Regular'],
      },
      spacing: {
        // কাস্টম স্পেসিং
        '18': '4.5rem',
        '88': '22rem',
      },
      borderRadius: {
        // কাস্টম বর্ডার রেডিয়াস
        '4xl': '2rem',
      },
      animation: {
        // কাস্টম অ্যানিমেশন
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' },
        },
      },
    },
  },
  plugins: [
    // প্রয়োজনীয় প্লাগইন যোগ করতে পারেন
    // require('@tailwindcss/forms'),
    // require('@tailwindcss/typography'),
    // require('@tailwindcss/aspect-ratio'),
  ],
}